from maix import image, camera, display, app, time
import cv2
import numpy as np

# 常量定义

# ==================== 辅助函数 ====================

# ==================== 初始化相机和显示 ====================
cam = camera.Camera(640, 480, image.Format.FMT_BGR888)
disp = display.Display()

# ==================== 辅助函数 ====================
def get_value_from_history(values, use_latest=True):
    """从历史记录中获取值，默认使用最新值"""
    if not values:
        return 0

    # 返回最新的值
    if use_latest or len(values) < 2:
        return values[-1]

    # 如果需要平滑值（备用），返回最近几个值的简单平均
    return int(sum(values[-3:]) / min(len(values), 3))

def calculate_area_ratio(area1, area2):
    """计算两个面积的比例，确保结果在0-1之间"""
    max_area = max(area1, area2)
    if max_area == 0:
        return 1.0  # 如果两个面积都为0，认为完全匹配
    return min(area1, area2) / max_area

def calculate_distance(point1, point2):
    """计算两点之间的欧几里得距离"""
    return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)

def is_duplicate_shape(cx, cy, area, detected_shapes, shape_vertices, duplicate_distance_threshold, duplicate_area_ratio):
    """检查是否是重复的形状"""
    for detected_cx, detected_cy, detected_area, detected_vertices in detected_shapes:
        # 如果类型相同
        if detected_vertices == shape_vertices:
            # 检查中心点距离和面积比例
            distance = calculate_distance((cx, cy), (detected_cx, detected_cy))
            area_ratio = calculate_area_ratio(area, detected_area)

            # 如果中心点距离很近且面积比例接近1，认为是重复
            if distance < duplicate_distance_threshold and area_ratio > duplicate_area_ratio:
                return True
    return False

def find_matching_shape_position(cx, cy, shape_type, shape_tracking_data, position_tolerance):
    """查找匹配的形状位置"""
    shape_position = None
    min_distance = float('inf')

    for pos in shape_tracking_data.keys():
        if pos[0] == shape_type:
            distance = calculate_distance((cx, cy), (pos[1], pos[2]))
            if distance < position_tolerance and distance < min_distance:
                min_distance = distance
                shape_position = pos

    return shape_position

def count_vertices_inside_circle(quad_approx, circle_cx, circle_cy, circle_radius):
    """计算四边形有多少个顶点在圆内"""
    vertices_inside = 0
    for vertex in quad_approx:
        # vertex是一个包含[x, y]的数组
        vertex_x, vertex_y = vertex[0][0], vertex[0][1]
        vertex_distance = calculate_distance((vertex_x, vertex_y), (circle_cx, circle_cy))
        if vertex_distance <= circle_radius:
            vertices_inside += 1
    return vertices_inside

def calculate_overlap_area(quad_approx, circle_cx, circle_cy, circle_radius):
    """
    计算四边形与圆形的重叠面积比例
    返回重叠面积占四边形面积的比例 (0.0 - 1.0)
    """
    try:
        # 计算四边形面积
        quad_area = cv2.contourArea(quad_approx)
        if quad_area <= 0:
            return 0.0

        # 创建一个足够大的画布来绘制形状
        canvas_size = max(800, int(circle_radius * 3))

        # 计算偏移量，将圆心放在画布中心
        offset_x = canvas_size // 2 - circle_cx
        offset_y = canvas_size // 2 - circle_cy

        # 调整四边形顶点坐标
        adjusted_quad = quad_approx.copy()
        for i in range(len(adjusted_quad)):
            adjusted_quad[i][0][0] += offset_x
            adjusted_quad[i][0][1] += offset_y

        # 调整圆心坐标
        adjusted_circle_cx = canvas_size // 2
        adjusted_circle_cy = canvas_size // 2

        # 绘制四边形
        quad_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
        # 转换轮廓格式：从 [[[x, y]], [[x, y]], ...] 到 [[x, y], [x, y], ...]
        adjusted_quad_points = adjusted_quad.reshape(-1, 2)
        cv2.fillPoly(quad_mask, [adjusted_quad_points], 255)

        # 绘制圆形
        circle_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
        cv2.circle(circle_mask, (adjusted_circle_cx, adjusted_circle_cy), circle_radius, 255, -1)

        # 计算重叠区域
        overlap_mask = cv2.bitwise_and(quad_mask, circle_mask)
        overlap_area = cv2.countNonZero(overlap_mask)

        # 计算重叠比例
        overlap_ratio = overlap_area / cv2.countNonZero(quad_mask) if cv2.countNonZero(quad_mask) > 0 else 0.0

        return min(overlap_ratio, 1.0)  # 确保不超过1.0

    except Exception as e:
        print(f"计算重叠面积时出错: {e}")
        return 0.0

def is_quad_inside_circle_hybrid(quad_approx, circle_cx, circle_cy, circle_radius,
                                min_vertices_conservative=2, min_vertices_aggressive=3):
    """
    混合策略判断四边形是否在圆内
    结合中心点检查和顶点检查

    参数:
    - min_vertices_conservative: 保守过滤时，中心在圆内需要的最少顶点数
    - min_vertices_aggressive: 积极过滤时，中心在圆内需要的最少顶点数
    """
    # 计算四边形中心点
    M = cv2.moments(quad_approx)
    if M["m00"] == 0:
        return False, "无法计算中心点"

    quad_cx = int(M["m10"] / M["m00"])
    quad_cy = int(M["m01"] / M["m00"])

    # 策略1: 检查中心点是否在圆内
    center_distance = calculate_distance((quad_cx, quad_cy), (circle_cx, circle_cy))
    center_inside = center_distance <= circle_radius

    # 策略2: 检查顶点在圆内的数量
    vertices_inside_count = count_vertices_inside_circle(quad_approx, circle_cx, circle_cy, circle_radius)

    # 混合判断逻辑
    if center_inside and vertices_inside_count >= min_vertices_aggressive:
        # 中心在圆内且满足积极过滤条件 -> 确定在圆内
        reason = f"中心在圆内(距离:{center_distance:.1f}<={circle_radius}) 且 {vertices_inside_count}/4 个顶点在圆内 (积极过滤)"
        return True, reason
    elif center_inside and vertices_inside_count >= min_vertices_conservative:
        # 中心在圆内且满足保守过滤条件 -> 可能在圆内
        reason = f"中心在圆内(距离:{center_distance:.1f}<{circle_radius}) 且 {vertices_inside_count}/4 个顶点在圆内 (保守过滤)"
        return True, reason
    elif vertices_inside_count == 4:
        # 所有顶点都在圆内 -> 确定在圆内（即使中心可能因为计算误差不在圆内）
        reason = f"所有4个顶点都在圆内 (中心距离:{center_distance:.1f})"
        return True, reason
    else:
        # 其他情况 -> 不在圆内
        reason = f"中心{'在圆内' if center_inside else '在圆外'}(距离:{center_distance:.1f}) 且仅 {vertices_inside_count}/4 个顶点在圆内"
        return False, reason

def is_quad_inside_circle_area_overlap(quad_approx, circle_cx, circle_cy, circle_radius, overlap_threshold=0.7):
    """
    基于面积重叠判断四边形是否在圆内

    参数:
    - overlap_threshold: 重叠面积比例阈值，超过此值认为四边形在圆内
    """
    overlap_ratio = calculate_overlap_area(quad_approx, circle_cx, circle_cy, circle_radius)

    if overlap_ratio >= overlap_threshold:
        reason = f"面积重叠比例: {overlap_ratio:.2f} >= {overlap_threshold} (面积重叠过滤)"
        return True, reason
    else:
        reason = f"面积重叠比例: {overlap_ratio:.2f} < {overlap_threshold}"
        return False, reason

def is_quad_inside_circle_comprehensive(quad_approx, circle_cx, circle_cy, circle_radius,
                                      min_vertices_conservative=2, min_vertices_aggressive=3,
                                      overlap_threshold=0.7):
    """
    综合策略判断四边形是否在圆内
    结合混合策略（中心点+顶点）和面积重叠检查

    优先级：
    1. 面积重叠检查（处理顶点都在圆外但面积高度重合的情况）
    2. 混合策略检查（中心点+顶点检查）
    """
    # 策略1: 面积重叠检查（优先级最高）
    area_inside, area_reason = is_quad_inside_circle_area_overlap(
        quad_approx, circle_cx, circle_cy, circle_radius, overlap_threshold
    )
    if area_inside:
        return True, area_reason

    # 策略2: 混合策略检查（中心点+顶点）
    hybrid_inside, hybrid_reason = is_quad_inside_circle_hybrid(
        quad_approx, circle_cx, circle_cy, circle_radius,
        min_vertices_conservative, min_vertices_aggressive
    )
    if hybrid_inside:
        return True, hybrid_reason

    # 都不满足条件，不在圆内
    return False, f"综合判断：{area_reason} 且 {hybrid_reason}"

def count_vertices_inside_polygon(quad_approx, polygon_approx):
    """计算四边形有多少个顶点在多边形内"""
    vertices_inside = 0
    try:
        print(f"    🔍 检查顶点: 四边形格式={quad_approx.shape}, 多边形格式={polygon_approx.shape}")
        for i, vertex in enumerate(quad_approx):
            # vertex是一个包含[x, y]的数组
            vertex_x, vertex_y = int(vertex[0][0]), int(vertex[0][1])
            # 使用OpenCV的pointPolygonTest检查点是否在多边形内
            # 确保坐标是整数类型
            result = cv2.pointPolygonTest(polygon_approx, (vertex_x, vertex_y), False)
            if result >= 0:
                vertices_inside += 1
                print(f"      顶点{i}({vertex_x}, {vertex_y}) 在多边形内, 结果={result}")
            else:
                print(f"      顶点{i}({vertex_x}, {vertex_y}) 在多边形外, 结果={result}")
    except Exception as e:
        print(f"    ❌ 顶点检查出错: {e}")
        print(f"    四边形类型: {type(quad_approx)}, 多边形类型: {type(polygon_approx)}")
        if hasattr(quad_approx, 'dtype'):
            print(f"    四边形dtype: {quad_approx.dtype}")
        if hasattr(polygon_approx, 'dtype'):
            print(f"    多边形dtype: {polygon_approx.dtype}")
        raise
    return vertices_inside

def calculate_polygon_overlap_area(quad_approx, polygon_approx):
    """
    计算四边形与多边形的重叠面积比例
    返回重叠面积占四边形面积的比例 (0.0 - 1.0)
    """
    try:
        # 计算四边形面积
        quad_area = cv2.contourArea(quad_approx)
        if quad_area <= 0:
            return 0.0

        # 计算所有点的边界框，确定偏移量
        # 安全地处理数组格式
        try:
            quad_points = quad_approx.reshape(-1, 2)
            polygon_points = polygon_approx.reshape(-1, 2)
            all_points = np.vstack([quad_points, polygon_points])
        except Exception as e:
            print(f"数组格式错误: {e}, quad_shape={quad_approx.shape}, polygon_shape={polygon_approx.shape}")
            return 0.0

        min_x, min_y = np.min(all_points, axis=0)
        max_x, max_y = np.max(all_points, axis=0)

        # 动态计算画布大小，确保足够大
        shape_width = max_x - min_x
        shape_height = max_y - min_y
        canvas_size = max(800, int(max(shape_width, shape_height) * 2 + 100))

        # 计算偏移量，将所有形状移到画布中心
        offset_x = canvas_size // 2 - (min_x + max_x) // 2
        offset_y = canvas_size // 2 - (min_y + max_y) // 2

        # 调整四边形顶点坐标
        adjusted_quad = quad_approx.copy()
        for i in range(len(adjusted_quad)):
            adjusted_quad[i][0][0] += offset_x
            adjusted_quad[i][0][1] += offset_y

        # 调整多边形顶点坐标
        adjusted_polygon = polygon_approx.copy()
        for i in range(len(adjusted_polygon)):
            adjusted_polygon[i][0][0] += offset_x
            adjusted_polygon[i][0][1] += offset_y

        # 绘制四边形
        quad_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
        adjusted_quad_points = adjusted_quad.reshape(-1, 2)
        cv2.fillPoly(quad_mask, [adjusted_quad_points], 255)

        # 绘制多边形
        polygon_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
        adjusted_polygon_points = adjusted_polygon.reshape(-1, 2)
        cv2.fillPoly(polygon_mask, [adjusted_polygon_points], 255)

        # 计算重叠区域
        overlap_mask = cv2.bitwise_and(quad_mask, polygon_mask)
        overlap_area = cv2.countNonZero(overlap_mask)

        # 计算重叠比例
        quad_pixels = cv2.countNonZero(quad_mask)
        overlap_ratio = overlap_area / quad_pixels if quad_pixels > 0 else 0.0

        # 添加调试信息
        print(f"  📐 多边形重叠计算: 重叠像素={overlap_area}, 四边形像素={quad_pixels}, 比例={overlap_ratio:.3f}")

        return min(overlap_ratio, 1.0)  # 确保不超过1.0

    except Exception as e:
        print(f"计算多边形重叠面积时出错: {e}")
        return 0.0

def is_quad_inside_polygon_hybrid(quad_approx, polygon_approx,
                                min_vertices_conservative=2, min_vertices_aggressive=3):
    """
    混合策略判断四边形是否在多边形内
    结合中心点检查和顶点检查

    参数:
    - min_vertices_conservative: 保守过滤时，中心在多边形内需要的最少顶点数
    - min_vertices_aggressive: 积极过滤时，中心在多边形内需要的最少顶点数
    """
    # 计算四边形中心点
    M = cv2.moments(quad_approx)
    if M["m00"] == 0:
        return False, "无法计算中心点"

    quad_cx = int(M["m10"] / M["m00"])
    quad_cy = int(M["m01"] / M["m00"])

    # 策略1: 检查中心点是否在多边形内
    center_inside = cv2.pointPolygonTest(polygon_approx, (quad_cx, quad_cy), False) >= 0

    # 策略2: 检查顶点在多边形内的数量
    vertices_inside_count = count_vertices_inside_polygon(quad_approx, polygon_approx)

    # 混合判断逻辑
    if center_inside and vertices_inside_count >= min_vertices_aggressive:
        # 中心在多边形内且满足积极过滤条件 -> 确定在多边形内
        reason = f"中心在多边形内 且 {vertices_inside_count}/4 个顶点在多边形内 (积极过滤)"
        return True, reason
    elif center_inside and vertices_inside_count >= min_vertices_conservative:
        # 中心在多边形内且满足保守过滤条件 -> 可能在多边形内
        reason = f"中心在多边形内 且 {vertices_inside_count}/4 个顶点在多边形内 (保守过滤)"
        return True, reason
    elif vertices_inside_count == 4:
        # 所有顶点都在多边形内 -> 确定在多边形内（即使中心可能因为计算误差不在多边形内）
        reason = f"所有4个顶点都在多边形内"
        return True, reason
    else:
        # 其他情况 -> 不在多边形内
        reason = f"中心{'在多边形内' if center_inside else '在多边形外'} 且仅 {vertices_inside_count}/4 个顶点在多边形内"
        return False, reason

def is_quad_inside_polygon_area_overlap(quad_approx, polygon_approx, overlap_threshold=0.7):
    """
    基于面积重叠判断四边形是否在多边形内

    参数:
    - overlap_threshold: 重叠面积比例阈值，超过此值认为四边形在多边形内
    """
    overlap_ratio = calculate_polygon_overlap_area(quad_approx, polygon_approx)

    if overlap_ratio >= overlap_threshold:
        reason = f"面积重叠比例: {overlap_ratio:.2f} >= {overlap_threshold} (面积重叠过滤)"
        return True, reason
    else:
        reason = f"面积重叠比例: {overlap_ratio:.2f} < {overlap_threshold}"
        return False, reason

def is_quad_inside_polygon_comprehensive(quad_approx, polygon_approx,
                                       min_vertices_conservative=2, min_vertices_aggressive=3,
                                       overlap_threshold=0.7):
    """
    综合策略判断四边形是否在多边形内
    结合混合策略（中心点+顶点）和面积重叠检查

    优先级：
    1. 面积重叠检查（处理顶点都在多边形外但面积高度重合的情况）
    2. 混合策略检查（中心点+顶点检查）
    """
    # 策略1: 面积重叠检查（优先级最高）
    area_inside, area_reason = is_quad_inside_polygon_area_overlap(
        quad_approx, polygon_approx, overlap_threshold
    )
    if area_inside:
        return True, area_reason

    # 策略2: 混合策略检查（中心点+顶点）
    hybrid_inside, hybrid_reason = is_quad_inside_polygon_hybrid(
        quad_approx, polygon_approx,
        min_vertices_conservative, min_vertices_aggressive
    )
    if hybrid_inside:
        return True, hybrid_reason

    # 都不满足条件，不在多边形内
    return False, f"综合判断：{area_reason} 且 {hybrid_reason}"

def process_retained_quadrilateral(quad_approx, cx, cy, area, is_max_rect, frame_count,
                                 shape_tracking_data, vertex_history, vertex_history_size,
                                 position_tolerance, last_frame_shapes, img_result, edge_history,
                                 edge_history_size, use_instant_values, max_rectangles=None,
                                 enable_distance_measurement=False, calibration_pixels=12.0,
                                 calibration_distance=200.0, distance_history=None,
                                 distance_history_size=5, enable_inner_shape_stats=False,
                                 inner_shapes_stats=None, img_cv=None, edges=None, min_area=100,
                                 detected_shapes=None, duplicate_distance_threshold=15,
                                 duplicate_area_ratio=0.8, enable_preprocess=True,
                                 preprocess_started=False, preprocess_stable_frames=0,
                                 preprocess_stable_threshold=2):
    """
    处理保留的四边形（未被过滤的四边形）
    进行完整的跟踪、绘制和计算
    """
    global current_distance, second_largest_rect_info

    shape = "Quad"  # 四边形
    # 对最大的两个矩形使用特殊颜色
    color = image.COLOR_YELLOW if is_max_rect else image.COLOR_GREEN

    # 提取顶点坐标列表
    vertices = [tuple(pt[0]) for pt in quad_approx]

    # 查找匹配的形状位置并更新跟踪数据
    shape_position = find_matching_shape_position(cx, cy, "Quad", shape_tracking_data, position_tolerance)
    shape_position, cx, cy, vertices = update_shape_tracking(
        shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
        vertex_history, vertex_history_size, "Quad"
    )

    # 记录该四边形到当前帧四边形列表
    if "Quad" not in last_frame_shapes:
        last_frame_shapes["Quad"] = []
    last_frame_shapes["Quad"].append((cx, cy, area))

    # 更新框内图形统计数据（只统计非最大矩形的四边形，且只在预处理完成后统计）
    if (enable_inner_shape_stats and not is_max_rect and inner_shapes_stats is not None and
        enable_preprocess and len(max_rectangles) == 2 and
        frame_count >= preprocess_start_frame + preprocess_stable_threshold):
        update_inner_shape_stats("Quad", vertices=vertices, inner_shapes_stats=inner_shapes_stats,
                                max_rectangles=max_rectangles, second_largest_rect_physical_length=second_largest_rect_physical_length)

    # 检查是否为第二大矩形并进行距离测量
    is_second_largest = False
    if (enable_distance_measurement and max_rectangles and
        len(max_rectangles) >= 2 and distance_history is not None):

        is_second_largest = is_second_largest_rectangle(
            cx, cy, area, max_rectangles,
            duplicate_distance_threshold, duplicate_area_ratio
        )

        if is_second_largest:
            # 计算最长边
            longest_edge = calculate_longest_edge(vertices)

            if longest_edge > 0:
                # 计算距离
                calculated_distance = calculate_distance_from_pixels(
                    longest_edge, calibration_pixels, calibration_distance
                )

                # 添加到历史记录进行平滑
                distance_history.append(calculated_distance)
                if len(distance_history) > distance_history_size:
                    distance_history.pop(0)

                # 计算平滑后的距离
                current_distance = sum(distance_history) / len(distance_history)

                # 保存第二大矩形信息
                second_largest_rect_info = {
                    'center': (cx, cy),
                    'longest_edge': longest_edge,
                    'distance': current_distance,
                    'vertices': vertices
                }

                # 在控制台打印距离信息
                print(f"第二大矩形距离测量: 最长边={longest_edge}px, 计算距离={calculated_distance:.2f}cm, 平滑距离={current_distance:.2f}cm")

                # 在图像上显示距离信息
                distance_text = f"Dist:{current_distance:.1f}cm"
                img_result.draw_string(cx-30, cy-30, distance_text, image.COLOR_ORANGE)

                # 标记为第二大矩形
                shape += "(2nd)"
                color = image.COLOR_ORANGE

    # 在控制台打印形状信息（只在预处理完成后打印）
    if enable_preprocess and len(max_rectangles) == 2 and frame_count >= preprocess_start_frame + preprocess_stable_threshold:
        print(f"\n检测到新的{shape}，面积: {int(area)}，拐点数量: 4")

    # 在图像上标记识别结果
    img_result.draw_string(cx-20, cy, shape, color)
    # 如果启用，显示轮廓面积
    if show_shape_areas:
        area_text = f"A:{int(area)}"
        img_result.draw_string(cx-20, cy+15, area_text, color)

    # 画出轮廓和拐点，并显示边长
    draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                          edge_history, edge_history_size, use_instant_values)

    # 在四边形区域内检测多边形（只在预处理完成后且提供了必要参数时）
    if (img_cv is not None and edges is not None and detected_shapes is not None and
        enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
        preprocess_stable_frames >= preprocess_stable_threshold):

        detect_polygons_in_quad_region(
            quad_approx, img_cv, edges, min_area, detected_shapes,
            duplicate_distance_threshold, duplicate_area_ratio, img_result,
            frame_count, shape_tracking_data, vertex_history, vertex_history_size,
            position_tolerance, last_frame_shapes, enable_preprocess,
            preprocess_started, max_rectangles, preprocess_stable_frames,
            preprocess_stable_threshold, enable_inner_shape_stats, inner_shapes_stats,
            second_largest_rect_physical_length
        )

    return shape_position, cx, cy, vertices

def update_shape_tracking(shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
                         vertex_history, vertex_history_size, shape_type):
    """更新形状跟踪数据"""
    if shape_position is None:
        # 创建新的位置标识
        shape_position = (shape_type, cx, cy)
        shape_tracking_data[shape_position] = frame_count

        # 初始化顶点历史记录
        if isinstance(vertices, list):
            vertex_history[shape_position] = [vertices]
    else:
        # 更新现有形状
        shape_tracking_data[shape_position] = frame_count

        # 更新顶点历史记录
        if isinstance(vertices, list):
            if shape_position in vertex_history:
                vertex_history[shape_position].append(vertices)
                if len(vertex_history[shape_position]) > vertex_history_size:
                    vertex_history[shape_position].pop(0)
            else:
                vertex_history[shape_position] = [vertices]

    return shape_position, cx, cy, vertices

def calculate_longest_edge(vertices):
    """计算形状的最长边长度"""
    if len(vertices) < 2:
        return 0

    max_length = 0
    for i in range(len(vertices)):
        pt1 = vertices[i]
        pt2 = vertices[(i+1) % len(vertices)]
        edge_length = calculate_distance(pt1, pt2)
        if edge_length > max_length:
            max_length = edge_length

    return max_length

def calculate_distance_from_pixels(current_pixels, calibration_pixels, calibration_distance):
    """根据像素值计算实际距离"""
    if current_pixels <= 0:
        return 0.0

    # 使用反比例关系：d₂ = (calibration_pixels × calibration_distance) / current_pixels
    calculated_distance = (calibration_pixels * calibration_distance) / current_pixels
    return calculated_distance

def is_second_largest_rectangle(cx, cy, area, max_rectangles, duplicate_distance_threshold, duplicate_area_ratio):
    """判断当前四边形是否为第二大矩形"""
    if len(max_rectangles) < 2:
        return False

    # 获取第二大矩形（索引为1）
    _, second_max_area, second_max_approx = max_rectangles[1]

    # 计算第二大矩形的中心点
    M_second = cv2.moments(second_max_approx)
    if M_second["m00"] == 0:
        return False

    cx_second = int(M_second["m10"] / M_second["m00"])
    cy_second = int(M_second["m01"] / M_second["m00"])

    # 计算距离和面积比
    distance = calculate_distance((cx, cy), (cx_second, cy_second))
    area_ratio = calculate_area_ratio(area, second_max_area)

    # 判断是否是同一个矩形
    return distance < duplicate_distance_threshold and area_ratio > duplicate_area_ratio

def calculate_all_edge_lengths(vertices):
    """计算形状所有边的长度"""
    if len(vertices) < 2:
        return []

    edge_lengths = []
    for i in range(len(vertices)):
        pt1 = vertices[i]
        pt2 = vertices[(i+1) % len(vertices)]
        edge_length = calculate_distance(pt1, pt2)
        edge_lengths.append(edge_length)

    return edge_lengths

def calculate_average_edge_length(vertices):
    """计算形状所有边的平均长度"""
    edge_lengths = calculate_all_edge_lengths(vertices)
    if not edge_lengths:
        return 0.0
    return sum(edge_lengths) / len(edge_lengths)

def calculate_physical_size_from_pixels(pixel_size, reference_pixels, reference_physical_size):
    """根据参考物理尺寸计算实际物理尺寸"""
    if reference_pixels <= 0:
        return 0.0
    return (pixel_size * reference_physical_size) / reference_pixels

def get_second_largest_rect_longest_edge(max_rectangles):
    """获取第二大矩形的最长边像素长度"""
    if len(max_rectangles) < 2:
        return 0.0

    # 获取第二大矩形的顶点
    _, _, approx = max_rectangles[1]  # 第二大矩形
    vertices = [tuple(pt[0]) for pt in approx]

    # 计算最长边
    return calculate_longest_edge(vertices)

def update_inner_shape_stats(shape_type, vertices=None, radius=None, inner_shapes_stats=None,
                            max_rectangles=None, second_largest_rect_physical_length=50.0):
    """更新框内图形统计数据，包括物理尺寸计算"""
    if inner_shapes_stats is None:
        return

    # 获取第二大矩形的最长边像素长度作为参考
    reference_pixels = get_second_largest_rect_longest_edge(max_rectangles) if max_rectangles else 0.0

    if shape_type == "Triangle" and vertices:
        # 计算三角形的平均边长（像素）
        avg_edge_pixels = calculate_average_edge_length(vertices)
        # 计算物理尺寸
        avg_edge_physical = calculate_physical_size_from_pixels(
            avg_edge_pixels, reference_pixels, second_largest_rect_physical_length
        ) if reference_pixels > 0 else 0.0

        inner_shapes_stats['triangles']['count'] += 1
        inner_shapes_stats['triangles']['total_edge_length'] += avg_edge_pixels
        inner_shapes_stats['triangles']['total_edge_length_physical'] += avg_edge_physical
        inner_shapes_stats['triangles']['avg_edge_length'] = (
            inner_shapes_stats['triangles']['total_edge_length'] /
            inner_shapes_stats['triangles']['count']
        )
        inner_shapes_stats['triangles']['avg_edge_length_physical'] = (
            inner_shapes_stats['triangles']['total_edge_length_physical'] /
            inner_shapes_stats['triangles']['count']
        )

    elif shape_type == "Quad" and vertices:
        # 计算四边形的平均边长（像素）
        avg_edge_pixels = calculate_average_edge_length(vertices)
        # 计算物理尺寸
        avg_edge_physical = calculate_physical_size_from_pixels(
            avg_edge_pixels, reference_pixels, second_largest_rect_physical_length
        ) if reference_pixels > 0 else 0.0

        inner_shapes_stats['quadrilaterals']['count'] += 1
        inner_shapes_stats['quadrilaterals']['total_edge_length'] += avg_edge_pixels
        inner_shapes_stats['quadrilaterals']['total_edge_length_physical'] += avg_edge_physical
        inner_shapes_stats['quadrilaterals']['avg_edge_length'] = (
            inner_shapes_stats['quadrilaterals']['total_edge_length'] /
            inner_shapes_stats['quadrilaterals']['count']
        )
        inner_shapes_stats['quadrilaterals']['avg_edge_length_physical'] = (
            inner_shapes_stats['quadrilaterals']['total_edge_length_physical'] /
            inner_shapes_stats['quadrilaterals']['count']
        )

    elif shape_type == "Circle" and radius:
        # 计算圆形的物理半径
        radius_physical = calculate_physical_size_from_pixels(
            radius, reference_pixels, second_largest_rect_physical_length
        ) if reference_pixels > 0 else 0.0

        inner_shapes_stats['circles']['count'] += 1
        inner_shapes_stats['circles']['total_radius'] += radius
        inner_shapes_stats['circles']['total_radius_physical'] += radius_physical
        inner_shapes_stats['circles']['avg_radius'] = (
            inner_shapes_stats['circles']['total_radius'] /
            inner_shapes_stats['circles']['count']
        )
        inner_shapes_stats['circles']['avg_radius_physical'] = (
            inner_shapes_stats['circles']['total_radius_physical'] /
            inner_shapes_stats['circles']['count']
        )

    elif shape_type == "Polygon" and vertices:
        # 计算多边形的平均边长（像素）
        avg_edge_pixels = calculate_average_edge_length(vertices)
        # 计算物理尺寸
        avg_edge_physical = calculate_physical_size_from_pixels(
            avg_edge_pixels, reference_pixels, second_largest_rect_physical_length
        ) if reference_pixels > 0 else 0.0

        inner_shapes_stats['polygons']['count'] += 1
        inner_shapes_stats['polygons']['total_edge_length'] += avg_edge_pixels
        inner_shapes_stats['polygons']['total_edge_length_physical'] += avg_edge_physical
        inner_shapes_stats['polygons']['avg_edge_length'] = (
            inner_shapes_stats['polygons']['total_edge_length'] /
            inner_shapes_stats['polygons']['count']
        )
        inner_shapes_stats['polygons']['avg_edge_length_physical'] = (
            inner_shapes_stats['polygons']['total_edge_length_physical'] /
            inner_shapes_stats['polygons']['count']
        )

def reset_inner_shape_stats(inner_shapes_stats):
    """重置框内图形统计数据"""
    inner_shapes_stats['triangles'] = {
        'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
        'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0
    }
    inner_shapes_stats['quadrilaterals'] = {
        'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
        'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0
    }
    inner_shapes_stats['circles'] = {
        'count': 0, 'avg_radius': 0.0, 'total_radius': 0.0,
        'avg_radius_physical': 0.0, 'total_radius_physical': 0.0
    }
    inner_shapes_stats['polygons'] = {
        'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
        'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0
    }

def collect_fine_polygons_from_quads(quadrilaterals, img_cv, edges, min_area,
                                    enable_preprocess, preprocess_started, max_rectangles,
                                    preprocess_stable_frames, preprocess_stable_threshold):
    """
    从所有四边形区域收集精细检测的多边形，用于四边形过滤
    返回: [(cx, cy, approx), ...] 格式的多边形列表
    """
    global use_roi_optimization, roi_valid, cached_roi_rect

    collected_polygons = []

    # 只在预处理完成后才进行多边形检测
    if not (enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
            preprocess_stable_frames >= preprocess_stable_threshold):
        return collected_polygons

    # 临时减小epsilon_factor以提高精度
    fine_epsilon_factor = 0.01

    try:
        for quad_info in quadrilaterals:
            quad_approx = quad_info['approx']

            # 在ROI模式下，检查四边形是否完全在ROI区域内
            if (use_roi_optimization and roi_valid and cached_roi_rect is not None and
                edges.shape != img_cv.shape[:2]):
                roi_x, roi_y, roi_w, roi_h = cached_roi_rect

                # 检查四边形的所有顶点是否都在ROI区域内
                all_vertices_in_roi = True
                for point in quad_approx:
                    px, py = point[0]
                    if not (roi_x <= px < roi_x + roi_w and roi_y <= py < roi_y + roi_h):
                        all_vertices_in_roi = False
                        break

                if not all_vertices_in_roi:
                    continue

            # 创建四边形区域的掩码
            if edges.shape != img_cv.shape[:2]:
                # ROI模式：调整坐标
                roi_x, roi_y, roi_w, roi_h = cached_roi_rect
                adjusted_quad = quad_approx.copy()
                for i in range(len(adjusted_quad)):
                    adjusted_quad[i][0][0] -= roi_x
                    adjusted_quad[i][0][1] -= roi_y

                mask = np.zeros(edges.shape, dtype=np.uint8)
                cv2.fillPoly(mask, [adjusted_quad.reshape(-1, 2)], 255)
            else:
                # 非ROI模式
                mask = np.zeros(edges.shape, dtype=np.uint8)
                cv2.fillPoly(mask, [quad_approx.reshape(-1, 2)], 255)

            # 在四边形区域内查找轮廓
            masked_edges = cv2.bitwise_and(edges, mask)
            contours, _ = cv2.findContours(masked_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                area = cv2.contourArea(contour)
                if area < min_area:
                    continue

                # 使用精细的epsilon_factor
                epsilon = fine_epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                num_vertices = len(approx)

                # 只收集多边形（5个或更多顶点）
                if num_vertices >= 5:
                    # 计算中心点
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])

                        # 如果是ROI模式，需要转换回原图坐标
                        if edges.shape != img_cv.shape[:2] and roi_valid and cached_roi_rect is not None:
                            roi_x, roi_y, roi_w, roi_h = cached_roi_rect
                            cx += roi_x
                            cy += roi_y

                            # 也需要调整approx坐标
                            adjusted_approx = approx.copy()
                            for i in range(len(adjusted_approx)):
                                adjusted_approx[i][0][0] += roi_x
                                adjusted_approx[i][0][1] += roi_y
                            approx = adjusted_approx

                        # 确保坐标格式正确：转换为整数并保持正确的数组格式
                        formatted_approx = np.array([[int(pt[0][0]), int(pt[0][1])] for pt in approx], dtype=np.int32)
                        formatted_approx = formatted_approx.reshape(-1, 1, 2)  # OpenCV期望的格式

                        collected_polygons.append((cx, cy, formatted_approx))
                        print(f"收集到精细多边形用于过滤: 中心({cx}, {cy}), 顶点数: {num_vertices}, 格式: {formatted_approx.shape}, 类型: {formatted_approx.dtype}")

    except Exception as e:
        print(f"收集精细多边形时出错: {e}")

    return collected_polygons

def detect_polygons_in_quad_region(quad_approx, img_cv, edges, min_area,
                                  detected_shapes, duplicate_distance_threshold, duplicate_area_ratio,
                                  img_result, frame_count, shape_tracking_data, vertex_history,
                                  vertex_history_size, position_tolerance, last_frame_shapes,
                                  enable_preprocess, preprocess_started, max_rectangles,
                                  preprocess_stable_frames, preprocess_stable_threshold,
                                  enable_inner_shape_stats=False, inner_shapes_stats=None,
                                  second_largest_rect_physical_length=50.0):
    """
    在四边形区域内检测多边形，使用更精细的epsilon_factor
    """
    global polygon_count, use_roi_optimization, roi_valid, cached_roi_rect

    # 只在预处理完成后才进行多边形检测
    if not (enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
            preprocess_stable_frames >= preprocess_stable_threshold):
        return

    # 在ROI模式下，检查四边形是否完全在ROI区域内
    if (use_roi_optimization and roi_valid and cached_roi_rect is not None and
        edges.shape != img_cv.shape[:2]):
        roi_x, roi_y, roi_w, roi_h = cached_roi_rect

        # 检查四边形的所有顶点是否都在ROI区域内
        all_vertices_in_roi = True
        for point in quad_approx:
            px, py = point[0]
            if not (roi_x <= px < roi_x + roi_w and roi_y <= py < roi_y + roi_h):
                all_vertices_in_roi = False
                break

        if not all_vertices_in_roi:
            print(f"跳过四边形内多边形检测：四边形超出ROI区域")
            return

    # 创建四边形区域的掩码
    # 需要根据edges的实际尺寸来创建掩码
    if edges.shape != img_cv.shape[:2]:
        # ROI模式：edges是ROI区域的尺寸，需要调整quad_approx坐标
        # 获取ROI偏移量
        if roi_valid and cached_roi_rect is not None:
            roi_x, roi_y, roi_w, roi_h = cached_roi_rect
            # 将quad_approx坐标转换为ROI坐标系
            adjusted_quad_approx = quad_approx.copy()
            adjusted_quad_approx[:, 0, 0] -= roi_x  # 调整x坐标
            adjusted_quad_approx[:, 0, 1] -= roi_y  # 调整y坐标

            # 创建ROI尺寸的掩码
            mask = np.zeros(edges.shape, dtype=np.uint8)
            cv2.fillPoly(mask, [adjusted_quad_approx], 255)
        else:
            print("警告：ROI模式下无法获取ROI信息，跳过多边形检测")
            return
    else:
        # 全图模式：直接使用原坐标
        mask = np.zeros(img_cv.shape[:2], dtype=np.uint8)
        cv2.fillPoly(mask, [quad_approx], 255)

    # 在四边形区域内查找轮廓
    masked_edges = cv2.bitwise_and(edges, mask)
    contours, _ = cv2.findContours(masked_edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)

    # 临时减小epsilon_factor以提高精度
    fine_epsilon_factor = 0.01

    try:
        for contour in contours:
            # 计算轮廓面积
            area = cv2.contourArea(contour)

            # 过滤掉太小的轮廓
            if area < min_area:
                continue

            # 使用更精细的epsilon_factor进行轮廓近似
            epsilon = fine_epsilon_factor * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            # 根据拐点数量识别形状
            num_vertices = len(approx)

            # 只检测多边形（5个或更多顶点）
            if num_vertices >= 5:
                # 计算轮廓的中心点
                M = cv2.moments(contour)
                if M["m00"] == 0:
                    continue

                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])

                # 如果是ROI模式，需要将坐标转换回原图坐标系
                if edges.shape != img_cv.shape[:2] and roi_valid and cached_roi_rect is not None:
                    roi_x, roi_y, roi_w, roi_h = cached_roi_rect
                    cx += roi_x
                    cy += roi_y
                    # 同时需要调整approx中的顶点坐标
                    adjusted_approx = approx.copy()
                    adjusted_approx[:, 0, 0] += roi_x
                    adjusted_approx[:, 0, 1] += roi_y
                    approx = adjusted_approx

                # 检查是否是重复的多边形
                is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, num_vertices,
                                                duplicate_distance_threshold, duplicate_area_ratio)

                # 如果是重复的，跳过
                if is_duplicate:
                    continue

                shape = f"Polygon({num_vertices})"  # 多边形，显示顶点数
                color = image.COLOR_PURPLE  # 使用紫色表示多边形
                polygon_count += 1

                # 添加到已检测形状列表
                detected_shapes.append((cx, cy, area, num_vertices))

                # 在控制台打印形状信息
                print(f"\n检测到新的{shape}，面积: {int(area)}，拐点数量: {num_vertices}")

                # 在图像上标记识别结果
                img_result.draw_string(cx-20, cy, shape, color)
                # 如果启用，显示轮廓面积
                if show_shape_areas:
                    area_text = f"A:{int(area)}"
                    img_result.draw_string(cx-20, cy+15, area_text, color)

                # 提取顶点坐标列表
                # 注意：如果进行了ROI坐标转换，approx已经是原图坐标系的了
                vertices = [tuple(pt[0]) for pt in approx]

                # 查找匹配的形状位置并更新跟踪数据
                shape_position = find_matching_shape_position(cx, cy, "Polygon", shape_tracking_data, position_tolerance)
                shape_position, cx, cy, vertices = update_shape_tracking(
                    shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
                    vertex_history, vertex_history_size, "Polygon"
                )

                # 记录该多边形到当前帧多边形列表
                if "Polygon" not in last_frame_shapes:
                    last_frame_shapes["Polygon"] = []
                last_frame_shapes["Polygon"].append((cx, cy, area))

                # 更新框内图形统计数据（只在预处理完成后统计）
                if enable_inner_shape_stats and enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
                    update_inner_shape_stats("Polygon", vertices=vertices, inner_shapes_stats=inner_shapes_stats,
                                            max_rectangles=max_rectangles, second_largest_rect_physical_length=second_largest_rect_physical_length)

                # 画出轮廓和拐点，并显示边长（与三角形保持一致）
                draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                                      edge_history, edge_history_size, use_instant_values)

    except Exception as e:
        print(f"四边形区域多边形检测出错: {e}")

def draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                         edge_history, edge_history_size, use_instant_values):
    """绘制形状边缘并计算显示边长"""
    for i in range(len(vertices)):
        pt1 = vertices[i]
        pt2 = vertices[(i+1) % len(vertices)]  # 连接到下一个点，形成闭环

        # 绘制线段（边）
        img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], color, 2)

        # 绘制拐点
        img_result.draw_circle(pt1[0], pt1[1], 3, color, thickness=-1)  # 实心圆

        # 计算边长（两点之间的欧几里得距离）
        edge_length = int(calculate_distance(pt1, pt2))

        # 使用形状位置作为键，确保同一位置的形状共享历史数据
        edge_key = (shape_position, i)
        if edge_key not in edge_history:
            edge_history[edge_key] = []

        # 添加当前边长到历史记录
        edge_history[edge_key].append(edge_length)

        # 保持历史记录在指定大小内
        if len(edge_history[edge_key]) > edge_history_size:
            edge_history[edge_key].pop(0)

        # 获取要显示的边长值（当前值或历史平均值）
        display_length = get_value_from_history(edge_history[edge_key], use_instant_values)

        # 计算边的中点坐标，用于显示边长
        mid_x = (pt1[0] + pt2[0]) // 2
        mid_y = (pt1[1] + pt2[1]) // 2

        # 在边的中点显示边长（根据参数控制）
        if show_edge_lengths:
            length_text = f"{display_length}"
            img_result.draw_string(mid_x, mid_y, length_text, image.COLOR_RED)

        # 在控制台打印边长信息（只在预处理完成后打印）
        if enable_preprocess and len(max_rectangles) == 2 and frame_count >= preprocess_start_frame + preprocess_stable_threshold:
            edge_info = f"{shape} Edge {i+1}: 当前={edge_length} pixels (from point {i} to point {(i+1) % len(vertices)})"
            print(edge_info)

# 设置参数 - 这些参数可能需要根据实际环境调整
binary_threshold = 100    # 二值化阈值
canny_low = 30           # Canny边缘检测低阈值
canny_high = 90         # Canny边缘检测高阈值
min_area = 100           # 最小轮廓面积，识别更小的轮廓
epsilon_factor = 0.1    # 轮廓近似精度因子

# ==================== 距离测量标定参数（重要！请根据实际情况修改）====================
calibration_distance = 130.0    # 标定时的实际距离（厘米）- 请修改为您的实际标定距离
calibration_pixels = 176.0       # 标定时第二大四边形最长边的像素值 - 请修改为您的实际测量值

# ==================== 物理尺寸标定参数（重要！请根据实际情况修改）====================
second_largest_rect_physical_length = 19.1    # 第二大框长边的实际物理长度（厘米）- 请修改为您的实际测量值

# 闭运算参数
enable_closing = False    # 是否启用闭运算
kernel_size = 3          # 闭运算的核大小，越大效果越明显

# 重复检测参数
duplicate_distance_threshold = 15  # 中心点距离阈值，小于此值认为可能是重复
duplicate_area_ratio = 0.8        # 面积比例阈值，如果面积比例在这个范围内，认为可能是重复

# 圆内四边形过滤参数（混合策略）
min_vertices_for_conservative_filter = 2  # 保守过滤：中心在圆内时，至少需要多少个顶点在圆内
min_vertices_for_aggressive_filter = 3   # 积极过滤：中心在圆内时，至少需要多少个顶点在圆内
area_overlap_threshold = 0.7             # 面积重叠阈值：重叠比例超过此值认为四边形在圆内

# 多边形内四边形过滤参数（混合策略）
min_vertices_for_polygon_conservative_filter = 2  # 保守过滤：中心在多边形内时，至少需要多少个顶点在多边形内
min_vertices_for_polygon_aggressive_filter = 3   # 积极过滤：中心在多边形内时，至少需要多少个顶点在多边形内
polygon_overlap_threshold = 0.7                  # 面积重叠阈值：重叠比例超过此值认为四边形在多边形内

# 边长数据参数
edge_history_size = 3   # 保存少量历史数据以备不时之需
edge_history = {}       # 存储边长历史数据的字典 {(shape_type, shape_position, edge_idx): [lengths...]}
circle_radius_history = {}  # 存储圆半径历史数据的字典 {(cx, cy): [radiuses...]}
# 形状位置跟踪参数
position_tolerance = 20  # 认为是同一个形状的位置容差（像素）
last_frame_shapes = {}   # 上一帧识别到的形状 {shape_type: [(cx, cy, area), ...]}
shape_tracking_data = {} # 形状跟踪数据，记录每个位置的形状历史 {(shape_type, cx, cy): frame_count}
# 顶点历史记录，用于跟踪变化
vertex_history = {}      # 顶点历史记录 {(shape_type, cx, cy): [vertices_list]}
vertex_history_size = 3  # 减少顶点历史记录数量，更关注最近的测量值
# 边长计算参数
use_instant_values = True  # 使用当前测量值而非平均值
# 形状超时参数
shape_timeout = 1       # 如果超过20帧未检测到，则清理该形状位置的跟踪数据

# ==================== 距离测量控制参数 ====================
enable_distance_measurement = True  # 是否启用距离测量功能

# ==================== 框内图形数据统计参数 ====================
enable_inner_shape_stats = True     # 是否启用框内图形数据统计
show_inner_shape_stats = True       # 是否在图像上显示框内图形统计信息

# ==================== 显示控制参数 ====================
show_edge_lengths = True            # 是否显示边长信息
show_shape_areas = False            # 是否显示形状面积信息


# 距离测量相关变量
current_distance = 0.0           # 当前计算的距离
distance_history = []            # 距离历史记录，用于平滑
distance_history_size = 5        # 距离历史记录大小
second_largest_rect_info = None  # 第二大矩形信息

# 框内图形统计相关变量
inner_shapes_stats = {           # 框内图形统计数据
    'triangles': {
        'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
        'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0
    },
    'quadrilaterals': {
        'count': 0, 'avg_edge_length': 0.0, 'total_edge_length': 0.0,
        'avg_edge_length_physical': 0.0, 'total_edge_length_physical': 0.0
    },
    'circles': {
        'count': 0, 'avg_radius': 0.0, 'total_radius': 0.0,
        'avg_radius_physical': 0.0, 'total_radius_physical': 0.0
    }
}

# 是否显示中间处理结果
show_debug = True
debug_view = 2  # 0: 原图, 1: 二值化, 2: 边缘, 3: 闭运算后

# 移除旧的show_area参数，现在使用show_shape_areas

# 计数变量，用于定时切换视图
view_switch_count = 0  # 视图切换计数器
frame_count = 0  # 总帧计数，用于预处理和形状跟踪

# 预处理相关参数
enable_preprocess = True  # 是否启用预处理筛选功能
preprocess_start_frame = 3  # 从第几帧开始启用预处理
max_rectangles = []  # 存储最大的两个矩形 [(contour, area, approx), ...]
preprocess_started = False  # 是否已经开始预处理
preprocess_stable_frames = 0  # 跟踪最大矩形保持稳定的帧数
preprocess_stable_threshold = 2  # 需要保持稳定的帧数阈值

# 缓存优化相关变量
cached_mask = None  # 缓存的掩码
cached_mask_valid = False  # 掩码是否有效
last_rectangles_centers = []  # 上一帧矩形的中心点，用于检测位置变化
rectangle_position_threshold = 10  # 矩形位置变化阈值（像素）

# ROI区域优化参数
roi_expand_pixels = 20  # 在最大框基础上向外扩展的像素数
use_roi_optimization = True  # 是否启用ROI区域优化
cached_roi_rect = None  # 缓存的ROI矩形区域 (x, y, w, h)
roi_valid = False  # ROI区域是否有效

while not app.need_exit():
    # 计时开始
    t_start = time.ticks_ms()

    # 读取图像
    img_maix = cam.read()
    
    # 将MaixPy图像转换为OpenCV格式
    t = time.ticks_ms()
    img_cv = image.image2cv(img_maix, ensure_bgr=True, copy=False)
    t_convert = time.ticks_ms() - t
    
    # 步骤1：图像二值化
    t = time.ticks_ms()
    gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

    # ROI优化：如果ROI有效且预处理完成，只处理ROI区域
    roi_offset_x, roi_offset_y = 0, 0  # ROI区域在原图中的偏移
    if (use_roi_optimization and roi_valid and
        enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
        preprocess_stable_frames >= preprocess_stable_threshold):

        x, y, w, h = cached_roi_rect
        # 裁剪灰度图到ROI区域
        gray_roi = gray[y:y+h, x:x+w]
        roi_offset_x, roi_offset_y = x, y

        # 对ROI区域进行二值化
        _, binary = cv2.threshold(gray_roi, binary_threshold, 255, cv2.THRESH_BINARY)
        print(f"ROI优化：只处理 {w}x{h} 区域，节省 {((gray.shape[0]*gray.shape[1] - w*h)/(gray.shape[0]*gray.shape[1])*100):.1f}% 计算量")
    else:
        # 处理整个图像
        _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
        gray_roi = gray

    t_binary = time.ticks_ms() - t
    
    # 步骤2：应用闭运算（膨胀后腐蚀）
    t = time.ticks_ms()
    if enable_closing:
        # 创建结构元素（核）
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        # 应用闭运算：先膨胀后腐蚀，填充小洞和连接相近区域
        binary_closed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    else:
        # 不需要copy，直接使用binary
        binary_closed = binary
    t_closing = time.ticks_ms() - t
    
    # 步骤3：边缘提取
    t = time.ticks_ms()
    blurred = cv2.GaussianBlur(binary_closed, (3, 3), 0)
    edges = cv2.Canny(blurred, canny_low, canny_high)
    t_edge = time.ticks_ms() - t
    
    # 步骤4：查找轮廓
    t = time.ticks_ms()
    contours, _ = cv2.findContours(edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)

    # 如果使用了ROI，需要将轮廓坐标转换回原图坐标系
    if (use_roi_optimization and roi_valid and
        enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
        preprocess_stable_frames >= preprocess_stable_threshold):

        # 将ROI区域内的轮廓坐标转换为原图坐标
        adjusted_contours = []
        for contour in contours:
            adjusted_contour = contour.copy()
            adjusted_contour[:, 0, 0] += roi_offset_x  # 调整x坐标
            adjusted_contour[:, 0, 1] += roi_offset_y  # 调整y坐标
            adjusted_contours.append(adjusted_contour)
        contours = adjusted_contours

    t_contour = time.ticks_ms() - t

    # 排序轮廓，从大到小
    contours = sorted(contours, key=cv2.contourArea, reverse=True)
    
    # 预处理步骤：识别最大的两个矩形并创建感兴趣区域
    if enable_preprocess and frame_count >= preprocess_start_frame:
        # 第一次运行预处理时，初始化最大矩形列表
        if not preprocess_started:
            print("开始预处理：识别最大的两个矩形")
            preprocess_started = True
            max_rectangles = []
            # 重置缓存
            cached_mask = None
            cached_mask_valid = False
            last_rectangles_centers = []
            # 重置ROI缓存
            cached_roi_rect = None
            roi_valid = False
        
        # 如果还没找到两个最大矩形，则尝试找出
        if len(max_rectangles) < 2:
            rect_found_this_frame = False
            for contour in contours:
                area = cv2.contourArea(contour)
                if area < min_area:
                    continue
                
                # 近似轮廓为多边形
                epsilon = epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # 如果是四边形
                if len(approx) == 4:
                    # 检查是否与已有的矩形重复
                    is_duplicate = False
                    for _, existing_area, existing_approx in max_rectangles:
                        # 计算中心点
                        M1 = cv2.moments(approx)
                        M2 = cv2.moments(existing_approx)
                        if M1["m00"] != 0 and M2["m00"] != 0:
                            cx1 = int(M1["m10"] / M1["m00"])
                            cy1 = int(M1["m01"] / M1["m00"])
                            cx2 = int(M2["m10"] / M2["m00"])
                            cy2 = int(M2["m01"] / M2["m00"])
                            
                            # 计算距离和面积比
                            distance = np.sqrt((cx1-cx2)**2 + (cy1-cy2)**2)
                            area_ratio = min(area, existing_area) / max(area, existing_area)
                            
                            # 判断是否重复
                            if distance < duplicate_distance_threshold and area_ratio > duplicate_area_ratio:
                                is_duplicate = True
                                break
                    
                    # 如果不是重复的且面积够大，加入最大矩形列表
                    if not is_duplicate:
                        max_rectangles.append((contour, area, approx))
                        max_rectangles = sorted(max_rectangles, key=lambda x: x[1], reverse=True)[:2]
                        rect_found_this_frame = True
                        print(f"找到新的大矩形，面积: {area}, 目前已找到 {len(max_rectangles)}/2")
                        
                        # 如果已经找到两个，打印确认信息
                        if len(max_rectangles) == 2:
                            print("已找到最大的两个矩形，开始筛选处理")
                            for i, (_, rect_area, _) in enumerate(max_rectangles):
                                print(f"矩形 {i+1} 面积: {rect_area}")
            
            # 如果本帧没有找到新矩形，重置稳定帧计数
            if not rect_found_this_frame and len(max_rectangles) < 2:
                preprocess_stable_frames = 0
                # 重置缓存，因为矩形配置可能发生变化
                cached_mask_valid = False
                roi_valid = False
        
        # 如果已经找到两个最大矩形，筛选轮廓
        if len(max_rectangles) == 2:
            # 检查矩形位置是否发生了显著变化
            current_centers = []
            rectangles_moved = False

            for _, _, approx in max_rectangles:
                M = cv2.moments(approx)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    current_centers.append((cx, cy))

            # 检查是否需要更新掩码
            need_update_mask = False
            if not cached_mask_valid or cached_mask is None:
                need_update_mask = True
                print("预处理：首次创建掩码")
            elif len(last_rectangles_centers) == len(current_centers):
                # 检查矩形位置是否发生显著变化
                for i, (cx, cy) in enumerate(current_centers):
                    if i < len(last_rectangles_centers):
                        last_cx, last_cy = last_rectangles_centers[i]
                        distance = np.sqrt((cx - last_cx)**2 + (cy - last_cy)**2)
                        if distance > rectangle_position_threshold:
                            need_update_mask = True
                            rectangles_moved = True
                            print(f"预处理：矩形{i+1}位置变化 {distance:.1f}像素，需要更新掩码")
                            break
            else:
                need_update_mask = True
                print("预处理：矩形数量变化，需要更新掩码")

            # 更新掩码（仅在需要时）
            if need_update_mask:
                try:
                    # 创建新的掩码
                    cached_mask = np.zeros_like(gray)

                    # 绘制两个大矩形到掩码上
                    for _, _, approx in max_rectangles:
                        cv2.drawContours(cached_mask, [approx], 0, 255, -1)

                    # 检查掩码是否有效
                    mask_pixels = np.sum(cached_mask > 0)
                    if mask_pixels < 100:
                        print(f"警告：生成的掩码像素太少 ({mask_pixels})，可能未正确识别矩形区域")
                        cached_mask_valid = False
                        preprocess_stable_frames = 0
                    else:
                        cached_mask_valid = True
                        last_rectangles_centers = current_centers.copy()
                        preprocess_stable_frames += 1
                        print(f"预处理：掩码更新成功，像素数: {mask_pixels}，掩码占比: {mask_pixels/(gray.shape[0]*gray.shape[1])*100:.2f}%")

                        # 计算ROI区域（如果启用ROI优化）
                        if use_roi_optimization:
                            try:
                                # 找到所有矩形点的边界
                                all_points = []
                                for _, _, approx in max_rectangles:
                                    for point in approx:
                                        all_points.append(point[0])

                                if all_points:
                                    all_points = np.array(all_points)
                                    min_x = max(0, np.min(all_points[:, 0]) - roi_expand_pixels)
                                    max_x = min(gray.shape[1], np.max(all_points[:, 0]) + roi_expand_pixels)
                                    min_y = max(0, np.min(all_points[:, 1]) - roi_expand_pixels)
                                    max_y = min(gray.shape[0], np.max(all_points[:, 1]) + roi_expand_pixels)

                                    cached_roi_rect = (int(min_x), int(min_y), int(max_x - min_x), int(max_y - min_y))
                                    roi_valid = True
                                    roi_area = (max_x - min_x) * (max_y - min_y)
                                    total_area = gray.shape[0] * gray.shape[1]
                                    roi_ratio = roi_area / total_area * 100
                                    print(f"预处理：ROI区域计算成功 ({min_x},{min_y},{max_x-min_x},{max_y-min_y})，占比: {roi_ratio:.1f}%")
                                else:
                                    roi_valid = False
                                    print("预处理：ROI计算失败，无有效矩形点")
                            except Exception as e:
                                print(f"ROI计算出错: {e}")
                                roi_valid = False

                except Exception as e:
                    print(f"掩码创建出错: {e}")
                    cached_mask_valid = False
                    preprocess_stable_frames = 0
                    # 重置ROI
                    roi_valid = False
            else:
                # 使用缓存的掩码，增加稳定帧计数
                preprocess_stable_frames += 1
                if preprocess_stable_frames % 10 == 0:  # 每10帧打印一次状态
                    mask_pixels = np.sum(cached_mask > 0)
                    print(f"预处理：使用缓存掩码，稳定度 {preprocess_stable_frames}，像素数: {mask_pixels}")

            # 应用掩码过滤（仅在掩码有效且稳定时）
            if cached_mask_valid and preprocess_stable_frames >= preprocess_stable_threshold:
                try:
                    # 筛选轮廓，只保留在掩码内的轮廓
                    filtered_contours = []
                    for contour in contours:
                        # 计算轮廓中心点
                        M = cv2.moments(contour)
                        if M["m00"] != 0:
                            cx = int(M["m10"] / M["m00"])
                            cy = int(M["m01"] / M["m00"])

                            # 检查中心点是否在掩码内
                            if 0 <= cy < cached_mask.shape[0] and 0 <= cx < cached_mask.shape[1] and cached_mask[cy, cx] > 0:
                                filtered_contours.append(contour)
                        else:
                            # 如果不能计算中心点，检查轮廓上的点是否在掩码内
                            inside_points = 0
                            for point in contour:
                                px, py = point[0]
                                if 0 <= py < cached_mask.shape[0] and 0 <= px < cached_mask.shape[1] and cached_mask[py, px] > 0:
                                    inside_points += 1
                            # 如果至少有一半的点在掩码内，认为这个轮廓在区域内
                            if inside_points >= len(contour) / 2:
                                filtered_contours.append(contour)

                    # 更新轮廓列表
                    original_count = len(contours)
                    contours = filtered_contours
                    if original_count != len(contours):
                        print(f"预处理：从 {original_count} 个轮廓筛选出 {len(contours)} 个位于主矩形内的轮廓")

                except Exception as e:
                    print(f"掩码应用出错: {e}")
            else:
                if not cached_mask_valid:
                    print("预处理：掩码无效，跳过过滤")
                else:
                    print(f"预处理：等待矩形区域稳定 ({preprocess_stable_frames}/{preprocess_stable_threshold})")
        else:
            # 如果还没有找到两个矩形，重置稳定帧计数
            preprocess_stable_frames = 0
            # 重置缓存
            cached_mask_valid = False
            roi_valid = False
    
    # 选择显示哪个视图
    if show_debug:
        if debug_view == 0:
            img_result = image.cv2image(img_cv, bgr=True, copy=False)
        elif debug_view == 1:
            # 将二值图像转换为可显示的MaixPy图像
            # 如果使用了ROI，需要将ROI区域的结果放回完整图像中
            if (use_roi_optimization and roi_valid and
                enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
                preprocess_stable_frames >= preprocess_stable_threshold):
                # 创建完整尺寸的二值图像
                full_binary = np.zeros_like(gray)
                x, y, w, h = cached_roi_rect
                # 确保binary的尺寸与ROI区域匹配
                if binary.shape[0] == h and binary.shape[1] == w:
                    full_binary[y:y+h, x:x+w] = binary
                else:
                    print(f"警告：binary尺寸 {binary.shape} 与ROI尺寸 ({h},{w}) 不匹配")
                    # 回退到直接使用binary
                    full_binary = binary if binary.shape == gray.shape else np.zeros_like(gray)
                binary_colored = np.stack([full_binary, full_binary, full_binary], axis=2)
            else:
                binary_colored = np.stack([binary, binary, binary], axis=2)
            img_result = image.cv2image(binary_colored)
        elif debug_view == 2:
            # 将边缘图像转换为可显示的MaixPy图像
            # 如果使用了ROI，需要将ROI区域的结果放回完整图像中
            if (use_roi_optimization and roi_valid and
                enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
                preprocess_stable_frames >= preprocess_stable_threshold):
                # 创建完整尺寸的边缘图像
                full_edges = np.zeros_like(gray)
                x, y, w, h = cached_roi_rect
                # 确保edges的尺寸与ROI区域匹配
                if edges.shape[0] == h and edges.shape[1] == w:
                    full_edges[y:y+h, x:x+w] = edges
                else:
                    print(f"警告：edges尺寸 {edges.shape} 与ROI尺寸 ({h},{w}) 不匹配")
                    # 回退到直接使用edges
                    full_edges = edges if edges.shape == gray.shape else np.zeros_like(gray)
                edges_colored = np.stack([full_edges, full_edges, full_edges], axis=2)
            else:
                edges_colored = np.stack([edges, edges, edges], axis=2)
            img_result = image.cv2image(edges_colored)
        else:  # debug_view == 3
            # 将闭运算后的二值图像转换为可显示的MaixPy图像
            # 如果使用了ROI，需要将ROI区域的结果放回完整图像中
            if (use_roi_optimization and roi_valid and
                enable_preprocess and preprocess_started and len(max_rectangles) == 2 and
                preprocess_stable_frames >= preprocess_stable_threshold):
                # 创建完整尺寸的闭运算图像
                full_binary_closed = np.zeros_like(gray)
                x, y, w, h = cached_roi_rect
                # 确保binary_closed的尺寸与ROI区域匹配
                if binary_closed.shape[0] == h and binary_closed.shape[1] == w:
                    full_binary_closed[y:y+h, x:x+w] = binary_closed
                else:
                    print(f"警告：binary_closed尺寸 {binary_closed.shape} 与ROI尺寸 ({h},{w}) 不匹配")
                    # 回退到直接使用binary_closed
                    full_binary_closed = binary_closed if binary_closed.shape == gray.shape else np.zeros_like(gray)
                closed_colored = np.stack([full_binary_closed, full_binary_closed, full_binary_closed], axis=2)
            else:
                closed_colored = np.stack([binary_closed, binary_closed, binary_closed], axis=2)
            img_result = image.cv2image(closed_colored)
    else:
        img_result = image.cv2image(img_cv, bgr=True, copy=False)
    
    # 步骤5：识别形状
    triangle_count = 0
    quadrilateral_count = 0
    circle_count = 0       # 添加圆形计数
    polygon_count = 0      # 添加多边形计数
    min_detected_area = float('inf')  # 跟踪检测到的最小面积

    # 重置框内图形统计数据（每帧开始时重置，为本帧统计做准备）
    if enable_inner_shape_stats:
        reset_inner_shape_stats(inner_shapes_stats)

    # 用于跟踪已检测到的形状，防止重复
    detected_shapes = []  # 将存储 (cx, cy, area, vertices)
    
    # 先识别所有四边形
    quadrilaterals = []
    
    for contour in contours:
        # 计算轮廓面积
        area = cv2.contourArea(contour)
        
        # 过滤掉太小的轮廓
        if area < min_area:
            continue
            
        # 近似轮廓为多边形
        epsilon = epsilon_factor * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 根据拐点数量识别形状
        num_vertices = len(approx)
        
        # 找出所有四边形并保存
        if num_vertices == 4:
            # 计算四边形的中心点
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                
                # 检查是否是最大的两个矩形之一
                is_max_rect = False
                if enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
                    for _, max_area, max_approx in max_rectangles:
                        # 计算最大矩形的中心点
                        M_max = cv2.moments(max_approx)
                        if M_max["m00"] != 0:
                            cx_max = int(M_max["m10"] / M_max["m00"])
                            cy_max = int(M_max["m01"] / M_max["m00"])
                            
                            # 计算距离和面积比
                            distance = calculate_distance((cx, cy), (cx_max, cy_max))
                            area_ratio = calculate_area_ratio(area, max_area)
                            
                            # 判断是否是同一个矩形
                            if distance < duplicate_distance_threshold and area_ratio > duplicate_area_ratio:
                                is_max_rect = True
                                break
                
                # 检查是否是重复的四边形
                is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, 4,
                                                duplicate_distance_threshold, duplicate_area_ratio)
                
                # 如果不是重复的，则添加到候选四边形列表（延迟详细处理）
                if not is_duplicate:
                    # 存储四边形信息，包含处理所需的所有数据
                    quad_info = {
                        'approx': approx,
                        'cx': cx,
                        'cy': cy,
                        'area': area,
                        'is_max_rect': is_max_rect
                    }
                    quadrilaterals.append(quad_info)
                    detected_shapes.append((cx, cy, area, num_vertices))
                    quadrilateral_count += 1
    
    # 存储检测到的圆形信息，用于后续过滤四边形
    detected_circles = []  # 存储 (cx, cy, radius) 信息

    # 存储检测到的多边形信息，用于后续过滤四边形
    detected_polygons = []  # 存储 (cx, cy, approx) 信息

    # 🔥 新增：收集四边形区域内的精细多边形用于过滤
    if quadrilaterals:  # 如果有四边形，先收集精细多边形
        fine_polygons = collect_fine_polygons_from_quads(
            quadrilaterals, img_cv, edges, min_area,
            enable_preprocess, preprocess_started, max_rectangles,
            preprocess_stable_frames, preprocess_stable_threshold
        )
        detected_polygons.extend(fine_polygons)
        print(f"收集到 {len(fine_polygons)} 个精细多边形用于四边形过滤")

    # 然后处理所有轮廓，识别三角形、圆形和多边形
    for contour in contours:
        # 计算轮廓面积
        area = cv2.contourArea(contour)
        
        # 过滤掉太小的轮廓
        if area < min_area:
            continue
            
        # 近似轮廓为多边形
        epsilon = epsilon_factor * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 根据拐点数量识别形状
        num_vertices = len(approx)
        
        # 计算周长
        perimeter = cv2.arcLength(contour, True)
        
        # 计算轮廓的中心点
        M = cv2.moments(contour)
        if M["m00"] == 0:
            continue  # 跳过无法计算中心点的轮廓
            
        cx = int(M["m10"] / M["m00"])
        cy = int(M["m01"] / M["m00"])
        
        # 检查这个轮廓是否在任何一个四边形内部
        is_inside_quad = False

        if (enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold):
            # 预处理完成后，检查是否在最大矩形内
            for _, _, max_approx in max_rectangles:
                if cv2.pointPolygonTest(max_approx, (int(cx), int(cy)), False) >= 0:
                    is_inside_quad = True
                    break
        else:
            # 预处理未完成，检查是否在任何四边形内
            for quad in quadrilaterals:
                # 使用OpenCV的pointPolygonTest检查点是否在多边形内
                # 如果返回值大于0，则点在多边形内部
                if cv2.pointPolygonTest(quad['approx'], (int(cx), int(cy)), False) > 0:
                    is_inside_quad = True
                    break
                
        # 只处理在四边形内部的形状
        if not is_inside_quad:
            continue
        
        # 更新检测到的最小面积
        if area < min_detected_area:
            min_detected_area = area
            
        # 识别圆形
        # 圆的特征是：面积与周长的关系接近于 4*π*area = perimeter²
        circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
        
        # 先检查是否是圆形（圆度接近1，表示非常接近圆形）
        if circularity > 0.85:  # 圆度阈值，可以根据实际情况调整
            # 检查是否是重复的圆形
            is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, -1,
                                            duplicate_distance_threshold, duplicate_area_ratio)

            # 如果是重复的，跳过
            if is_duplicate:
                continue
            
            shape = "Circle"  # 圆形
            color = image.COLOR_BLUE
            circle_count += 1
            
            # 添加到已检测形状列表，使用-1表示圆形
            detected_shapes.append((cx, cy, area, -1))

            # 计算圆形半径并记录，用于后续过滤四边形
            radius = int(np.sqrt(area / np.pi))
            detected_circles.append((cx, cy, radius))
            
        # 如果为多边形（5个或更多拐点）
        elif num_vertices >= 5:
            # 检查是否是重复的多边形
            is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, num_vertices,
                                            duplicate_distance_threshold, duplicate_area_ratio)

            # 如果是重复的，跳过
            if is_duplicate:
                continue

            shape = f"Polygon({num_vertices})"  # 多边形，显示顶点数
            color = image.COLOR_PURPLE  # 使用紫色表示多边形
            polygon_count += 1

            # 添加到已检测形状列表
            detected_shapes.append((cx, cy, area, num_vertices))

            # 存储多边形信息，用于后续过滤四边形
            detected_polygons.append((cx, cy, approx))

        # 如果为三角形（3个拐点）
        elif num_vertices == 3:
            # 检查是否是重复的三角形
            is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, 3,
                                            duplicate_distance_threshold, duplicate_area_ratio)

            # 如果是重复的，跳过
            if is_duplicate:
                continue

            shape = "Triangle"  # 三角形
            color = image.COLOR_RED
            triangle_count += 1

            # 添加到已检测形状列表
            detected_shapes.append((cx, cy, area, num_vertices))

        else:
            # 不是三角形也不是圆形，跳过
            continue
        
        # 在控制台打印形状信息（只在预处理完成后打印）
        if enable_preprocess and len(max_rectangles) == 2 and frame_count >= preprocess_start_frame + preprocess_stable_threshold:
            if shape == "Circle":
                print(f"\n检测到新的{shape}，面积: {int(area)}，圆度: {circularity:.3f}")
            else:
                print(f"\n检测到新的{shape}，面积: {int(area)}，拐点数量: {num_vertices}")
        
        # 在图像上标记识别结果
        img_result.draw_string(cx-20, cy, shape, color)
        # 如果启用，显示轮廓面积
        if show_shape_areas:
            area_text = f"A:{int(area)}"
            img_result.draw_string(cx-20, cy+15, area_text, color)
        
        # 如果是三角形，处理和显示边长
        if shape == "Triangle":
            # 提取顶点坐标列表
            vertices = [tuple(pt[0]) for pt in approx]

            # 查找匹配的形状位置并更新跟踪数据
            shape_position = find_matching_shape_position(cx, cy, "Triangle", shape_tracking_data, position_tolerance)
            shape_position, cx, cy, vertices = update_shape_tracking(
                shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
                vertex_history, vertex_history_size, "Triangle"
            )

            # 记录该三角形到当前帧三角形列表
            if "Triangle" not in last_frame_shapes:
                last_frame_shapes["Triangle"] = []
            last_frame_shapes["Triangle"].append((cx, cy, area))

            # 更新框内图形统计数据（只在预处理完成后统计）
            if enable_inner_shape_stats and enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
                update_inner_shape_stats("Triangle", vertices=vertices, inner_shapes_stats=inner_shapes_stats,
                                        max_rectangles=max_rectangles, second_largest_rect_physical_length=second_largest_rect_physical_length)

            # 画出轮廓和拐点，并显示边长
            draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                                  edge_history, edge_history_size, use_instant_values)


        
        # 如果是圆形，计算和显示半径
        elif shape == "Circle":
            # 查找匹配的形状位置并更新跟踪数据
            shape_position = find_matching_shape_position(cx, cy, "Circle", shape_tracking_data, position_tolerance)
            shape_position, cx, cy, _ = update_shape_tracking(
                shape_position, cx, cy, None, frame_count, shape_tracking_data,
                vertex_history, vertex_history_size, "Circle"
            )
            
            # 记录该圆形到当前帧圆形列表
            if "Circle" not in last_frame_shapes:
                last_frame_shapes["Circle"] = []
            last_frame_shapes["Circle"].append((cx, cy, area))

            # 计算等效半径（基于面积的圆半径）
            radius = int(np.sqrt(area / np.pi))

            # 使用形状位置作为键，确保同一位置的圆形共享历史数据
            if shape_position not in circle_radius_history:
                circle_radius_history[shape_position] = []

            # 添加当前半径到历史记录
            circle_radius_history[shape_position].append(radius)

            # 保持历史记录在指定大小内
            if len(circle_radius_history[shape_position]) > edge_history_size:
                circle_radius_history[shape_position].pop(0)

            # 使用改进的平均值计算方法计算平均半径
            display_radius = get_value_from_history(circle_radius_history[shape_position], use_instant_values)

            # 更新框内图形统计数据（只在预处理完成后统计）
            if enable_inner_shape_stats and enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
                update_inner_shape_stats("Circle", radius=display_radius, inner_shapes_stats=inner_shapes_stats,
                                        max_rectangles=max_rectangles, second_largest_rect_physical_length=second_largest_rect_physical_length)

            # 绘制圆形轮廓
            img_result.draw_circle(cx, cy, display_radius, color, thickness=2)

            # 绘制圆心
            img_result.draw_circle(cx, cy, 3, color, thickness=-1)

            # 显示半径信息（根据参数控制）
            if show_edge_lengths:
                radius_text = f"R:{display_radius}"
                img_result.draw_string(cx + 5, cy, radius_text, image.COLOR_RED)

            # 在控制台打印半径信息（只在预处理完成后打印）
            if enable_preprocess and len(max_rectangles) == 2 and frame_count >= preprocess_start_frame + preprocess_stable_threshold:
                print(f"Circle Radius: {radius} pixels")

        # 如果是多边形，处理和显示
        elif shape.startswith("Polygon"):
            # 提取顶点坐标列表
            vertices = [tuple(pt[0]) for pt in approx]

            # 查找匹配的形状位置并更新跟踪数据
            shape_position = find_matching_shape_position(cx, cy, "Polygon", shape_tracking_data, position_tolerance)
            shape_position, cx, cy, vertices = update_shape_tracking(
                shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
                vertex_history, vertex_history_size, "Polygon"
            )

            # 记录该多边形到当前帧多边形列表
            if "Polygon" not in last_frame_shapes:
                last_frame_shapes["Polygon"] = []
            last_frame_shapes["Polygon"].append((cx, cy, area))

            # 画出轮廓和拐点（多边形不计算边长，只显示形状）
            for i in range(len(vertices)):
                pt1 = vertices[i]
                pt2 = vertices[(i+1) % len(vertices)]  # 连接到下一个点，形成闭环

                # 绘制线段（边）
                img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], color, 2)

                # 绘制拐点
                img_result.draw_circle(pt1[0], pt1[1], 3, color, thickness=-1)  # 实心圆

    # 过滤在圆形和多边形内部的四边形并处理保留的四边形
    print(f"🔍 四边形过滤检查: 检测到 {len(detected_circles)} 个圆形, {len(detected_polygons)} 个多边形, {len(quadrilaterals)} 个四边形")

    if detected_circles or detected_polygons:
        retained_quads = []
        removed_quad_count = 0
        filtered_quad_centers = set()  # 收集被过滤四边形的中心点
        filtered_quad_positions = []   # 收集需要清理的四边形位置信息

        for quad_info in quadrilaterals:
            quad_approx = quad_info['approx']
            quad_cx = quad_info['cx']
            quad_cy = quad_info['cy']
            quad_area = quad_info['area']
            is_max_rect = quad_info['is_max_rect']

            # 使用综合策略检查四边形是否在任何圆形或多边形内部
            is_inside_shape = False
            filter_reason = ""

            # 检查是否在圆形内部
            for circle_cx, circle_cy, circle_radius in detected_circles:
                inside, reason = is_quad_inside_circle_comprehensive(
                    quad_approx, circle_cx, circle_cy, circle_radius,
                    min_vertices_for_conservative_filter, min_vertices_for_aggressive_filter,
                    area_overlap_threshold
                )
                if inside:
                    is_inside_shape = True
                    filter_reason = reason
                    print(f"过滤掉在圆形内部的四边形: {reason}")
                    break

            # 如果不在圆形内部，检查是否在多边形内部
            if not is_inside_shape:
                for polygon_cx, polygon_cy, polygon_approx in detected_polygons:
                    inside, reason = is_quad_inside_polygon_comprehensive(
                        quad_approx, polygon_approx,
                        min_vertices_for_polygon_conservative_filter, min_vertices_for_polygon_aggressive_filter,
                        polygon_overlap_threshold
                    )
                    if inside:
                        is_inside_shape = True
                        filter_reason = reason
                        print(f"过滤掉在多边形内部的四边形: {reason}")
                        break

            # 如果不在任何圆形或多边形内部，处理这个四边形
            if not is_inside_shape:
                # 对保留的四边形进行完整处理
                process_retained_quadrilateral(
                    quad_approx, quad_cx, quad_cy, quad_area, is_max_rect, frame_count,
                    shape_tracking_data, vertex_history, vertex_history_size,
                    position_tolerance, last_frame_shapes, img_result, edge_history,
                    edge_history_size, use_instant_values, max_rectangles,
                    enable_distance_measurement, calibration_pixels,
                    calibration_distance, distance_history, distance_history_size,
                    enable_inner_shape_stats, inner_shapes_stats, img_cv, edges, min_area,
                    detected_shapes, duplicate_distance_threshold, duplicate_area_ratio,
                    enable_preprocess, preprocess_started, preprocess_stable_frames,
                    preprocess_stable_threshold
                )
                retained_quads.append(quad_approx)
            else:
                # 收集被过滤四边形的中心点信息（不进行任何处理）
                filtered_quad_centers.add((quad_cx, quad_cy))
                quad_position = ("Quad", quad_cx, quad_cy)
                filtered_quad_positions.append(quad_position)
                removed_quad_count += 1

        # 更新四边形列表和计数（只保留轮廓信息用于后续清理）
        quadrilaterals = retained_quads
        quadrilateral_count -= removed_quad_count
    else:
        # 没有检测到圆形，处理所有四边形
        retained_quads = []
        for quad_info in quadrilaterals:
            quad_approx = quad_info['approx']
            quad_cx = quad_info['cx']
            quad_cy = quad_info['cy']
            quad_area = quad_info['area']
            is_max_rect = quad_info['is_max_rect']

            # 对所有四边形进行完整处理
            process_retained_quadrilateral(
                quad_approx, quad_cx, quad_cy, quad_area, is_max_rect, frame_count,
                shape_tracking_data, vertex_history, vertex_history_size,
                position_tolerance, last_frame_shapes, img_result, edge_history,
                edge_history_size, use_instant_values, max_rectangles,
                enable_distance_measurement, calibration_pixels,
                calibration_distance, distance_history, distance_history_size,
                enable_inner_shape_stats, inner_shapes_stats, img_cv, edges, min_area,
                detected_shapes, duplicate_distance_threshold, duplicate_area_ratio,
                enable_preprocess, preprocess_started, preprocess_stable_frames,
                preprocess_stable_threshold
            )
            retained_quads.append(quad_approx)

        quadrilaterals = retained_quads
        filtered_quad_centers = set()
        filtered_quad_positions = []
        removed_quad_count = 0

    if removed_quad_count > 0:
        # 从detected_shapes中移除被过滤的四边形
        filtered_detected_shapes = []
        for detected_cx, detected_cy, detected_area, detected_vertices in detected_shapes:
            if detected_vertices == 4:  # 四边形
                # 检查这个四边形的中心点是否在被过滤的集合中
                if (detected_cx, detected_cy) not in filtered_quad_centers:
                    filtered_detected_shapes.append((detected_cx, detected_cy, detected_area, detected_vertices))
            else:
                # 非四边形，保留
                filtered_detected_shapes.append((detected_cx, detected_cy, detected_area, detected_vertices))

        detected_shapes = filtered_detected_shapes

    # 同时需要从last_frame_shapes中移除被过滤的四边形
    if "Quad" in last_frame_shapes and removed_quad_count > 0:
        filtered_quad_shapes = []
        for quad_cx, quad_cy, quad_area in last_frame_shapes["Quad"]:
            # 检查这个四边形的中心点是否在被过滤的集合中
            if (quad_cx, quad_cy) not in filtered_quad_centers:
                filtered_quad_shapes.append((quad_cx, quad_cy, quad_area))

        last_frame_shapes["Quad"] = filtered_quad_shapes

    # 清理被过滤四边形的所有跟踪数据
    if filtered_quad_positions:
        for quad_pos in filtered_quad_positions:
            try:
                # 清理形状跟踪数据
                if quad_pos in shape_tracking_data:
                    shape_tracking_data.pop(quad_pos, None)
                    print(f"清理被过滤四边形的跟踪数据: 位置 ({quad_pos[1]}, {quad_pos[2]})")

                # 清理顶点历史记录
                if quad_pos in vertex_history:
                    vertex_history.pop(quad_pos, None)

                # 清理边长历史记录
                edge_keys_to_remove = []
                for key in edge_history.keys():
                    if isinstance(key, tuple) and len(key) >= 1 and key[0] == quad_pos:
                        edge_keys_to_remove.append(key)

                for key in edge_keys_to_remove:
                    edge_history.pop(key, None)

            except Exception as e:
                print(f"清理被过滤四边形数据时出错: {e}, 位置: {quad_pos}")

    if removed_quad_count > 0:
        print(f"总共过滤掉 {removed_quad_count} 个在圆形或多边形内部的四边形")

    # 只显示必要的信息（预处理完成后）
    if enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
        y_offset = 5

        # 显示ROI优化状态
        if use_roi_optimization and roi_valid:
            x, y, w, h = cached_roi_rect
            roi_info = f"ROI: {w}x{h} ({(w*h)/(gray.shape[0]*gray.shape[1])*100:.0f}%)"
            img_result.draw_string(5, y_offset, roi_info, image.COLOR_GREEN)
            y_offset += 15

        # 显示距离测量信息
        if enable_distance_measurement and current_distance > 0:
            distance_info = f"Distance: {current_distance:.1f}cm"
            img_result.draw_string(5, y_offset, distance_info, image.COLOR_ORANGE)
            y_offset += 20

        # 显示第二大矩形参考信息
        if len(max_rectangles) >= 2:
            reference_pixels = get_second_largest_rect_longest_edge(max_rectangles)
            if reference_pixels > 0:
                ref_info = f"Reference: 2nd Rect Edge = {reference_pixels:.1f}px = {second_largest_rect_physical_length:.1f}cm"
                img_result.draw_string(5, y_offset, ref_info, image.COLOR_YELLOW)
                y_offset += 15

        # 显示框内图形统计信息
        if enable_inner_shape_stats and show_inner_shape_stats:
            # 显示三角形统计
            if inner_shapes_stats['triangles']['count'] > 0:
                tri_info = f"Triangles: {inner_shapes_stats['triangles']['count']}, Avg Edge: {inner_shapes_stats['triangles']['avg_edge_length']:.1f}px ({inner_shapes_stats['triangles']['avg_edge_length_physical']:.2f}cm)"
                img_result.draw_string(5, y_offset, tri_info, image.COLOR_RED)
                y_offset += 15

            # 显示四边形统计
            if inner_shapes_stats['quadrilaterals']['count'] > 0:
                quad_info = f"Quads: {inner_shapes_stats['quadrilaterals']['count']}, Avg Edge: {inner_shapes_stats['quadrilaterals']['avg_edge_length']:.1f}px ({inner_shapes_stats['quadrilaterals']['avg_edge_length_physical']:.2f}cm)"
                img_result.draw_string(5, y_offset, quad_info, image.COLOR_GREEN)
                y_offset += 15

            # 显示圆形统计
            if inner_shapes_stats['circles']['count'] > 0:
                circle_info = f"Circles: {inner_shapes_stats['circles']['count']}, Avg Radius: {inner_shapes_stats['circles']['avg_radius']:.1f}px ({inner_shapes_stats['circles']['avg_radius_physical']:.2f}cm)"
                img_result.draw_string(5, y_offset, circle_info, image.COLOR_BLUE)
                y_offset += 15
    

    
    # 只在预处理未完成时显示预处理状态
    if enable_preprocess and not (preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold):
        if not preprocess_started:
            preprocess_text = f"Preprocessing: Waiting (Frame {frame_count}/{preprocess_start_frame})"
        elif len(max_rectangles) < 2:
            preprocess_text = f"Preprocessing: Finding rectangles ({len(max_rectangles)}/2)"
        elif preprocess_stable_frames < preprocess_stable_threshold:
            preprocess_text = f"Preprocessing: Stabilizing ({preprocess_stable_frames}/{preprocess_stable_threshold})"
        img_result.draw_string(5, 5, preprocess_text, image.COLOR_YELLOW)

    # 打印帧分隔符和统计信息（只在预处理完成后打印框内统计）
    print(f"\n---------- 帧 {frame_count} ----------")
    if enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
        print(f"检测到 {triangle_count} 个三角形, {quadrilateral_count} 个四边形, {circle_count} 个圆形, {polygon_count} 个多边形")

        # 打印框内图形统计信息
        if enable_inner_shape_stats:
            print(f"\n框内图形统计:")
            if inner_shapes_stats['triangles']['count'] > 0:
                print(f"  三角形: {inner_shapes_stats['triangles']['count']} 个, 平均边长: {inner_shapes_stats['triangles']['avg_edge_length']:.2f} 像素 ({inner_shapes_stats['triangles']['avg_edge_length_physical']:.2f} 厘米)")
            if inner_shapes_stats['quadrilaterals']['count'] > 0:
                print(f"  四边形: {inner_shapes_stats['quadrilaterals']['count']} 个, 平均边长: {inner_shapes_stats['quadrilaterals']['avg_edge_length']:.2f} 像素 ({inner_shapes_stats['quadrilaterals']['avg_edge_length_physical']:.2f} 厘米)")
            if inner_shapes_stats['circles']['count'] > 0:
                print(f"  圆形: {inner_shapes_stats['circles']['count']} 个, 平均半径: {inner_shapes_stats['circles']['avg_radius']:.2f} 像素 ({inner_shapes_stats['circles']['avg_radius_physical']:.2f} 厘米)")
    else:
        print(f"预处理阶段 - 检测到 {triangle_count} 个三角形, {quadrilateral_count} 个四边形, {circle_count} 个圆形, {polygon_count} 个多边形")
    
    # 清除所有调试信息显示 - 只保留必要的信息
    
    # 显示结果
    disp.show(img_result)
    
    # 在预处理完成后，突出显示两个最大矩形框
    if enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
        # 在图像上绘制两个最大矩形的轮廓，使用明显的颜色
        for i, (_, _, approx) in enumerate(max_rectangles):
            # 转换为点列表
            points = [tuple(pt[0]) for pt in approx]
            # 使用红色和蓝色分别标记两个矩形
            highlight_color = image.COLOR_RED if i == 0 else image.COLOR_BLUE
            # 绘制粗线条轮廓
            for j in range(len(points)):
                pt1 = points[j]
                pt2 = points[(j+1) % len(points)]
                img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], highlight_color, 3)
            
            # 在矩形上标注其序号
            M = cv2.moments(approx)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                img_result.draw_string(cx-30, cy, f"主矩形 {i+1}", highlight_color)

        # 绘制ROI边界（如果启用ROI优化）
        if use_roi_optimization and roi_valid:
            x, y, w, h = cached_roi_rect
            # 绘制ROI边界矩形（使用四条线段）
            img_result.draw_line(x, y, x+w, y, image.COLOR_GREEN, 2)  # 上边
            img_result.draw_line(x+w, y, x+w, y+h, image.COLOR_GREEN, 2)  # 右边
            img_result.draw_line(x+w, y+h, x, y+h, image.COLOR_GREEN, 2)  # 下边
            img_result.draw_line(x, y+h, x, y, image.COLOR_GREEN, 2)  # 左边
            # 在ROI左上角标注
            img_result.draw_string(x+5, y+5, "ROI", image.COLOR_GREEN)

        # 可视化掩码区域（半透明）
        show_mask_debug = False  # 控制是否显示掩码调试视图
        if show_mask_debug:
            mask = np.zeros_like(gray)
            for _, _, approx in max_rectangles:
                cv2.drawContours(mask, [approx], 0, 255, -1)
            
            # 转换掩码为彩色
            mask_colored = np.zeros_like(img_cv)
            mask_colored[mask > 0] = [0, 200, 0]  # 绿色
            
            # 叠加到原图
            overlay = cv2.addWeighted(img_cv, 1.0, mask_colored, 0.3, 0)
            img_overlay = image.cv2image(overlay, bgr=True, copy=False)
            
            # 显示叠加后的图像
            disp.show(img_overlay)
            time.sleep_ms(5)  # 短暂延时确保显示
        else:
            # 重新显示带有突出显示的图像
            disp.show(img_result)
    
    # 清理长时间未检测到的形状数据
    expired_positions = []
    for pos, last_seen_frame in shape_tracking_data.items():
        if frame_count - last_seen_frame > shape_timeout:
            expired_positions.append(pos)
    
    # 从跟踪数据中移除过期的形状位置
    for pos in expired_positions:
        try:
            if pos in shape_tracking_data:
                shape_type = pos[0]
                shape_tracking_data.pop(pos, None)
                print(f"清理过期形状: {shape_type} 在位置 ({pos[1]}, {pos[2]})")
                
            # 清理对应的顶点历史数据
            if pos in vertex_history:
                vertex_history.pop(pos, None)
            
            # 清理对应的边长历史数据
            edge_keys_to_remove = []
            for edge_key in edge_history:
                if edge_key[0] == pos:
                    edge_keys_to_remove.append(edge_key)
            
            for key in edge_keys_to_remove:
                edge_history.pop(key, None)
            
            # 清理对应的圆半径历史数据
            if pos in circle_radius_history:
                circle_radius_history.pop(pos, None)
        except Exception as e:
            print(f"清理形状数据时出错: {e}, 位置: {pos}")
            
    if expired_positions:
        print(f"清理了 {len(expired_positions)} 个过期形状位置")
    
    # 重置当前帧形状列表，准备下一帧
    last_frame_shapes = {}
    
    # 增加总帧计数，用于预处理和形状跟踪
    frame_count += 1
    
    # 使用单独的计数器控制视图切换
    view_switch_count += 1
    if view_switch_count >= 10:  # 每10帧切换一次视图
        view_switch_count = 0
        if show_debug:
            # 确保视图模式始终在有效范围内
            debug_view = (debug_view + 1) % 4  # 现在有4种视图模式
            print(f"切换视图模式到: {['原图', '二值图', '边缘图', '闭运算图'][debug_view]}")
    
    # 简短延时，防止程序占用过多资源
    # 使用0毫秒延时让出CPU时间片给其他任务，但不会实际暂停程序
    time.sleep_ms(0)